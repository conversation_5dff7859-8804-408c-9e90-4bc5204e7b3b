"use client"

import { useState } from "react";

// Disable static generation for this page since it uses tRPC
export const dynamic = 'force-dynamic';
// import { useUser } from "@clerk/nextjs";
import DailyView from "@/components/daily-view";
import TaskInputModal from "@/components/task-input-modal";
import AutoPlanModal from "@/components/auto-plan-modal";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { trpc } from "@/utils/trpc";
import { toast } from "sonner";

// Helper function to convert database task to UI format
const convertTaskToUIFormat = (dbTask: any) => ({
  id: dbTask.id,
  title: dbTask.title,
  description: dbTask.description || '',
  time: dbTask.dueDate ? new Date(dbTask.dueDate).toTimeString().slice(0, 5) : '09:00',
  isCompleted: dbTask.status === 'done',
  isHighlight: dbTask.isHighlight,
  priorityScore: dbTask.priorityScore,
  status: dbTask.status,
});

export default function Home() {
  // const { isSignedIn, user } = useUser();
  const isSignedIn = true; // Mock for development
  const user = { firstName: "Dev", lastName: "User" }; // Mock for development
  
  // Fetch tasks from database
  const { data: dbTasks = [], refetch: refetchTasks } = trpc.tasks.list.useQuery(
    { includeSubtasks: false }, // Only get main tasks for daily view
    { 
      enabled: isSignedIn && typeof window !== 'undefined' // Disable during SSR
    }
  );
  
  // Convert database tasks to UI format
  const tasks = dbTasks.map(convertTaskToUIFormat);
  const [showTaskInput, setShowTaskInput] = useState(false);
  const [showAutoPlan, setShowAutoPlan] = useState(false);
  const [selectedTaskForAutoPlan, setSelectedTaskForAutoPlan] = useState<string | null>(null);

  // tRPC mutations
  const updateTaskMutation = trpc.tasks.update.useMutation({
    onSuccess: () => {
      refetchTasks();
      toast.success("Task updated successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to update task: " + error.message);
    },
  });

  const updateStatusMutation = trpc.tasks.updateStatus.useMutation({
    onSuccess: () => {
      refetchTasks();
      toast.success("Task status updated");
    },
    onError: (error: Error) => {
      toast.error("Failed to update status: " + error.message);
    },
  });

  const toggleHighlightMutation = trpc.tasks.toggleHighlight.useMutation({
    onSuccess: () => {
      refetchTasks();
    },
    onError: (error: Error) => {
      toast.error("Failed to toggle highlight: " + error.message);
    },
  });

  const handleTaskToggle = (taskId: string) => {
    const task = tasks.find((t: any) => t.id === taskId);
    if (task) {
      const newStatus = task.isCompleted ? 'todo' : 'done';
      updateStatusMutation.mutate({ id: taskId, status: newStatus });
    }
  };

  const handleTaskHighlight = (taskId: string) => {
    toggleHighlightMutation.mutate({ id: taskId });
  };

  const handleTaskDetails = (taskId: string) => {
    console.log('Opening task details for:', taskId);
    // TODO: Implement task details modal
  };

  const handleAutoLan = (taskId: string) => {
    setSelectedTaskForAutoPlan(taskId);
    setShowAutoPlan(true);
  };

  const handleTaskMove = (taskId: string, newTime: string) => {
    // Convert time back to ISO date string
    const today = new Date();
    const [hours, minutes] = newTime.split(':');
    today.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    
    updateTaskMutation.mutate({
      id: taskId,
      dueDate: today.toISOString(),
    });
  };

  const createManyTasksMutation = trpc.tasks.createMany.useMutation({
    onSuccess: () => {
      refetchTasks();
      toast.success("Tasks created successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to create tasks: " + error.message);
    },
  });

  const autoPlanMutation = trpc.tasks.autoPlan.useMutation({
    onSuccess: () => {
      refetchTasks();
      toast.success("Auto-plan completed and subtasks created");
    },
    onError: (error: Error) => {
      toast.error("Failed to create auto-plan: " + error.message);
    },
  });

  const handleTaskCreate = (parsedTasks: any[]) => {
    const tasksToCreate = parsedTasks.map(task => ({
      title: task.title,
      description: task.description || '',
      dueDate: task.dueDate || undefined,
      priorityScore: (task.priority || 3) * 20,
      status: 'todo' as const,
    }));
    
    createManyTasksMutation.mutate({ tasks: tasksToCreate });
  };

  const handleSubtasksCreate = (subtasks: any[]) => {
    const subtasksToCreate = subtasks.map((subtask, index) => {
      const futureTime = new Date(Date.now() + (index + 1) * 60 * 60 * 1000);
      return {
        title: subtask.title,
        description: subtask.description || '',
        dueDate: futureTime.toISOString(),
        priorityScore: subtask.priority * 20,
        status: 'todo' as const,
        parentTaskId: selectedTaskForAutoPlan || undefined,
      };
    });
    
    createManyTasksMutation.mutate({ tasks: subtasksToCreate });
  };

  if (!isSignedIn) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Welcome to Orion</h1>
          <p className="text-muted-foreground">Please sign in to continue</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="relative h-full">
      <DailyView
        tasks={tasks}
        onTaskToggle={handleTaskToggle}
        onTaskHighlight={handleTaskHighlight}
        onTaskDetails={handleTaskDetails}
        onAutoLan={handleAutoLan}
        onTaskMove={handleTaskMove}
      />
      
      {/* Floating Action Button */}
      <Button
        onClick={() => setShowTaskInput(true)}
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow z-40"
        size="icon"
      >
        <Plus className="h-6 w-6" />
      </Button>

      {/* Task Input Modal */}
      <TaskInputModal
        isOpen={showTaskInput}
        onClose={() => setShowTaskInput(false)}
        onTaskCreate={handleTaskCreate}
      />

      {/* Auto Plan Modal */}
      {selectedTaskForAutoPlan && (
        <AutoPlanModal
          isOpen={showAutoPlan}
          onClose={() => {
            setShowAutoPlan(false);
            setSelectedTaskForAutoPlan(null);
          }}
          taskId={selectedTaskForAutoPlan}
          taskTitle={tasks.find((t: any) => t.id === selectedTaskForAutoPlan)?.title || ''}
          taskDescription={tasks.find((t: any) => t.id === selectedTaskForAutoPlan)?.description}
          onSubtasksCreate={handleSubtasksCreate}
        />
      )}
    </div>
  );
}
