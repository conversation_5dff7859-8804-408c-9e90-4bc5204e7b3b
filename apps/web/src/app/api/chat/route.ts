import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { streamText } from 'ai';
// import { auth } from '@clerk/nextjs/server';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    // const { userId } = await auth();
    const userId = "mock-user-id"; // Mock for development

    // if (!userId) {
    //   return new Response('Unauthorized', { status: 401 });
    // }

    const { messages } = await req.json();

    const result = streamText({
      model: openrouter.chat('google/gemini-2.0-flash-exp:free'),
      messages,
      system: 'You are <PERSON>, an AI assistant for solo founders. Help users manage tasks, break down complex work, and prioritize based on their life milestones. Be concise, actionable, and focused on productivity.',
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}