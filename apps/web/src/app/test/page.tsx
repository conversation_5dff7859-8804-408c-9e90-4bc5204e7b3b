"use client";

import { trpc } from "@/utils/trpc";

export default function TestPage() {
  console.log("🧪 Test page rendered");

  // Test the simple endpoint
  const { data: testData, error: testError, isLoading: testLoading } = trpc.test.useQuery();

  // Test the health check
  const { data: healthData, error: healthError, isLoading: healthLoading } = trpc.healthCheck.useQuery();

  // Test the context endpoint
  const { data: contextData, error: contextError, isLoading: contextLoading } = trpc.contextTest.useQuery();

  console.log("🧪 Test results:", {
    testData,
    testError,
    testLoading,
    healthData,
    healthError,
    healthLoading,
    contextData,
    contextError,
    contextLoading
  });

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">tRPC Test Page</h1>
      
      <div className="space-y-4">
        <div className="border p-4 rounded">
          <h2 className="font-semibold">Health Check</h2>
          {healthLoading && <p>Loading...</p>}
          {healthError && <p className="text-red-500">Error: {healthError.message}</p>}
          {healthData && <p className="text-green-500">Success: {healthData}</p>}
        </div>

        <div className="border p-4 rounded">
          <h2 className="font-semibold">Test Endpoint</h2>
          {testLoading && <p>Loading...</p>}
          {testError && <p className="text-red-500">Error: {testError.message}</p>}
          {testData && (
            <div className="text-green-500">
              <p>Success: {testData.message}</p>
              <p>Timestamp: {testData.timestamp}</p>
            </div>
          )}
        </div>

        <div className="border p-4 rounded">
          <h2 className="font-semibold">Context Test</h2>
          {contextLoading && <p>Loading...</p>}
          {contextError && <p className="text-red-500">Error: {contextError.message}</p>}
          {contextData && (
            <div className="text-green-500">
              <p>Success: {contextData.message}</p>
              <p>Has User: {contextData.hasUser ? 'Yes' : 'No'}</p>
              <p>Has Session: {contextData.hasSession ? 'Yes' : 'No'}</p>
              <p>Timestamp: {contextData.timestamp}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
