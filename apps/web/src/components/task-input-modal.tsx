"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Lo<PERSON>, Send, X, Spark<PERSON>, Calendar, Clock } from "lucide-react";
import { trpc } from "@/utils/trpc";
import { toast } from "sonner";

interface ParsedTask {
  title: string;
  description?: string;
  dueDate?: string;
  estimatedTime?: string;
  priority?: number;
}

interface TaskInputModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskCreate?: (tasks: ParsedTask[]) => void; // Optional since tRPC handles persistence
}

export default function TaskInputModal({
  isOpen,
  onClose,
  onTaskCreate
}: TaskInputModalProps) {
  const [input, setInput] = useState("");
  const [parsedTasks, setParsedTasks] = useState<ParsedTask[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  // tRPC mutation for parsing tasks
  const parseTasksMutation = trpc.tasks.parseTasks.useMutation({
    onSuccess: (data: any) => {
      setParsedTasks(data.tasks || []);
      setShowPreview(true);
      toast.success("Tasks parsed successfully!");
    },
    onError: (error: Error) => {
      console.error('Error parsing tasks:', error);
      toast.error("Failed to parse tasks: " + error.message);
      // Fallback to simple parsing
      const fallbackTask: ParsedTask = {
        title: input,
        description: "Parsed from natural language input",
        priority: 3,
      };
      setParsedTasks([fallbackTask]);
      setShowPreview(true);
    },
  });

  const parseNaturalLanguage = (text: string) => {
    parseTasksMutation.mutate({
      input: text,
      saveToDatabase: true,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || parseTasksMutation.isPending) return;

    parseNaturalLanguage(input.trim());
  };

  const handleConfirm = () => {
    // Since tRPC already handles persistence, we just need to:
    // 1. Optionally call the legacy callback for compatibility
    // 2. Close the modal
    
    if (onTaskCreate) {
      onTaskCreate(parsedTasks);
    }
    
    toast.success(`${parsedTasks.length} tasks created successfully!`);
    handleClose();
  };

  const handleClose = () => {
    setInput("");
    setParsedTasks([]);
    setShowPreview(false);
    onClose();
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return null;
    try {
      return new Date(dateStr).toLocaleDateString();
    } catch {
      return dateStr;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Add Tasks Naturally
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-4">
          {!showPreview ? (
            <>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Describe what you need to do in natural language:
                </p>
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>• "Finish Stripe onboarding, then email investors tomorrow"</p>
                  <p>• "Schedule dentist appointment for next week"</p>
                  <p>• "Review pull requests and deploy to staging"</p>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="relative">
                  <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Type your tasks here..."
                    className="pr-12"
                    disabled={parseTasksMutation.isPending}
                    autoFocus
                  />
                  <Button
                    type="submit"
                    size="sm"
                    className="absolute right-1 top-1 h-8 w-8 p-0"
                    disabled={!input.trim() || parseTasksMutation.isPending}
                  >
                    {parseTasksMutation.isPending ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={!input.trim() || parseTasksMutation.isPending}>
                    {parseTasksMutation.isPending ? "Processing..." : "Parse Tasks"}
                  </Button>
                </div>
              </form>
            </>
          ) : (
            <>
              <div className="space-y-2">
                <h3 className="font-medium">Parsed Tasks</h3>
                <p className="text-sm text-muted-foreground">
                  Review and confirm the tasks below:
                </p>
              </div>

              <div className="space-y-3">
                {parsedTasks.map((task, index) => (
                  <Card key={index} className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium">{task.title}</h4>
                        {task.priority && (
                          <Badge variant="secondary">
                            Priority {task.priority}
                          </Badge>
                        )}
                      </div>

                      {task.description && (
                        <p className="text-sm text-muted-foreground">
                          {task.description}
                        </p>
                      )}

                      <div className="flex gap-4 text-xs text-muted-foreground">
                        {task.dueDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(task.dueDate)}
                          </div>
                        )}
                        {task.estimatedTime && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {task.estimatedTime}
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="flex justify-between pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(false)}
                >
                  Edit Input
                </Button>
                <div className="space-x-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button onClick={handleConfirm}>
                    Create {parsedTasks.length} Task{parsedTasks.length !== 1 ? 's' : ''}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}