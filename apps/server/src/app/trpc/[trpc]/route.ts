import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/routers';
import { createContext } from '@/lib/context';
import { NextRequest } from 'next/server';

function handler(req: NextRequest) {
  console.log('🚀 tRP<PERSON> Handler called:', {
    method: req.method,
    url: req.url,
    pathname: req.nextUrl.pathname,
    timestamp: new Date().toISOString()
  });

  return fetchRequestHandler({
    endpoint: '/trpc',
    req,
    router: appRouter,
    createContext: async () => {
      console.log('🔧 Creating context...');
      try {
        const context = await createContext(req);
        console.log('✅ Context created successfully:', {
          hasUser: !!context.user,
          hasSession: !!context.session,
          userId: context.session?.userId
        });
        return context;
      } catch (error) {
        console.error('❌ Context creation failed:', error);
        throw error;
      }
    },
    onError: ({ error, path, input }) => {
      console.error('🚨 tRPC Error:', {
        path,
        input,
        error: error.message,
        stack: error.stack
      });
    }
  });
}
export { handler as GET, handler as POST };
