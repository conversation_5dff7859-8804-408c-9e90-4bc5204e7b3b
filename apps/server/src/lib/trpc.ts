import { initTRPC, TRPCError } from "@trpc/server";
import type { Context } from "./context";

export const t = initTRPC.context<Context>().create();

export const router = t.router;

export const publicProcedure = t.procedure;

// Protected procedure that requires authentication - COMMENTED OUT FOR TESTING
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  // COMMENTED OUT FOR TESTING - Auth check disabled
  // if (!ctx.session?.userId) {
  //   throw new TRPCError({
  //     code: "UNAUTHORIZED",
  //     message: "Authentication required",
  //   });
  // }

  console.log("Protected procedure called with context:", {
    hasUser: !!ctx.user,
    hasSession: !!ctx.session,
    userId: ctx.session?.userId
  });

  return next({
    ctx: {
      ...ctx,
      session: ctx.session,
      user: ctx.user!,
    },
  });
});

