Product Requirements Document  
Project Codename: Orion

1. Purpose  
Build a mobile-friendly, AI-powered to-do application that lets a solo founder capture tasks in natural language and automatically plan, prioritize, and execute them every day.

2. Goals & Success Metrics  
- Daily active users ≥ 70 % of weekly active users after 60 days  
- Average time to first “Auto Plan” ≤ 2 minutes from sign-up  
- 90 % of new tasks successfully parsed from plain text without manual correction  
- Retention D30 ≥ 40 %

3. Target User  
Solo founders or small-team operators juggling product, growth, and admin work who need a single place to create, order, and execute tasks.

4. Core User Stories  
- As a founder, I can type “Finish Stripe onboarding, then email investors tomorrow” and see two scheduled tasks with correct dates.  
- As a founder, I can tap the “wand” icon on any task to break it into actionable subtasks.  
- As a founder, I can declare my life milestones (e.g., “Reach \$10 k MRR”, “Launch iOS app”) and have every task automatically prioritized against them.  
- As a founder, I can pick one “Daily Highlight” so I never lose sight of the most important outcome.  
- As a founder, I can connect external tools (GitHub, Google Flights, Notion, etc.) so Auto Plan can generate subtasks that include live links and commands.

5. Functional Requirements  
5.1 Accounts & Auth  
- Sign-up / sign-in via Clerk (email/pass, OAuth).  
- Multi-device session management.  

5.2 Profile  
- CRUD life milestones (title, description, target date, weight 1-5).  
- Toggle experimental features.

5.3 Task Model  
Properties: id, title, description, dueDate, priorityScore, isHighlight, parentTaskId, createdAt, status, toolAction (optional JSON payload).

5.4 Daily View (MVP)  
- Responsive timeline (00:00-24:00) showing tasks in chronological order.  
- Swipe right → schedule for tomorrow.  
- Tap diamond → open task details.  
- Tap star → set “Daily Highlight” (only one allowed).  
- Long-press / hover → preview Auto Plan.

5.5 Input Modal / NL Agent  
- Free-form text box with send button.  
- AISDK call chain:  
  1. classify intent (create, modify, query)  
  2. extract structured JSON (tasks, dates)  
  3. commit to Convex.

5.6 Auto Plan  
- UI: “⚡ Auto Plan” button inside task drawer.  
- Backend flow:  
  1. Fetch task context + user milestones + tool connections.  
  2. Call Gemini 2.5 Pro with structured prompt.  
  3. Receive list of subtasks {title, estimatedTime, suggestedTool}.  
  4. Persist as child tasks; surface chips linking to actions (GitHub PR, flight search, etc.).  

5.7 Priority Engine  
- Score = Σ wᵢ × rᵢ where wᵢ is milestone weight and rᵢ is relevance \(0 ≤ rᵢ ≤ 1\).  
- Break ties by dueDate asc, createdAt asc.  
- Recompute on: task add/update, milestone add/update, Auto Plan completion.

5.8 MCP (Multi-Capability Plugin) Connections  
- OAuth flow per integration, persisted as `ToolConnection` {provider, token, scopes}.  
- First-class integrations in MVP: GitHub, Google Calendar, Notion.  
- AISDK Actions: each provider exposes `createPR`, `createCalendarEvent`, etc.  

6. Non-Functional Requirements  
- PWA with full offline support (Convex optimistic updates).  
- Mobile first: <100 ms interaction latency on mid-tier devices.  
- All AI calls <3 s p95.  
- Zero PII stored in prompt text; use task IDs + metadata.  

7. Tech Stack (locked)  
- Monorepo: Turborepo  
- Frontend & routing: Next.js 15 / App Router / Bun runtime  
- State & server functions: Convex  
- Auth: Clerk  
- AI: AISDK + OpenRouter (Gemini 2.5 Flash for CRUD, Gemini 2.5 Pro for Auto Plan)  
- Package manager: Bun  
- Type safety: TypeScript 5.x  
- CI/CD: GitHub Actions → Vercel (preview) → Fly.io (edge workers)

8. Data Schema (Convex) – TypeScript  

```ts
// ./convex/schema.ts
import { defineSchema, s } from 'convex/schema';

export default defineSchema({
  users: {
    clerkId: s.string(),
    name: s.string(),
    email: s.string(),
    createdAt: s.number(),
  },
  milestones: {
    userId: s.id('users'),
    title: s.string(),
    description: s.optional(s.string()),
    targetDate: s.optional(s.string()),
    weight: s.number(), // 1-5
    createdAt: s.number(),
  },
  tasks: {
    userId: s.id('users'),
    parentTaskId: s.optional(s.id('tasks')),
    title: s.string(),
    description: s.optional(s.string()),
    dueDate: s.optional(s.string()),
    priorityScore: s.number(),
    isHighlight: s.boolean(),
    status: s.string(), // todo | doing | done
    toolAction: s.optional(s.record(s.any())),
    createdAt: s.number(),
  },
  toolConnections: {
    userId: s.id('users'),
    provider: s.string(), // github, notion, gcal
    accessToken: s.string(),
    scopes: s.array(s.string()),
    createdAt: s.number(),
  },
});
```

9. API Routes (Next.js / src/app/api)  
- `POST /api/tasks` → create via NL agent  
- `POST /api/tasks/:id/auto-plan` → invoke Auto Plan  
- `PATCH /api/tasks/:id/highlight` → set highlight  
- Webhooks for GitHub, Notion, Google Calendar to sync status

10. Milestones & Timeline (10-week roadmap)  
Week 1-2  
- Repo scaffolding (Turborepo), Clerk auth, Convex schema  
- Basic task CRUD + responsive Daily View

Week 3-4  
- NL agent MVP (Gemini Flash)  
- Life milestones UI  
- Priority engine v0

Week 5-6  
- Auto Plan v1 (Gemini Pro, no integrations)  
- Daily Highlight star  
- PWA offline caching

Week 7-8  
- GitHub, Google Calendar connections  
- Auto Plan v2 with live tool actions  
- Performance hardening

Week 9  
- Closed beta onboarding, metrics instrumentation

Week 10  
- Public launch on Product Hunt  

11. Risks & Mitigations  
- Risk: Gemini latency spikes → fallback to Flash with smaller prompt window.  
- Risk: Mis-prioritized tasks anger users → expose manual drag-and-drop as escape hatch.  
- Risk: OAuth scope creep → default read-only and request write scopes lazily.

12. Future (Post-MVP, out of scope)  
- Smart time-boxing (calendar auto-blocks)  
- Team sharing & task assignment  
- Native iOS / Android wrappers  

Ownership  
PM: You (solo founder)  
Tech Lead: You  
Design: Figma community kit + open-source iconography

Acceptance  
MVP is considered complete when a new user can:  
1. Sign up, add a milestone, create three tasks in NL.  
2. Tap Auto Plan on one task and see at least two subtasks.  
3. Mark a Daily Highlight and watch the Daily View re-order by priorityScore.

That’s the PRD v1. Iterate, slice scope, and ship fast.