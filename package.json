{"name": "LovAI", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate"}, "dependencies": {"@clerk/nextjs": "^6.23.1", "@openrouter/ai-sdk-provider": "^0.7.2", "ai": "^4.3.16", "openai": "^5.8.2", "zod": "^3.25.67"}, "devDependencies": {"turbo": "^2.5.4"}, "packageManager": "bun@1.2.15"}