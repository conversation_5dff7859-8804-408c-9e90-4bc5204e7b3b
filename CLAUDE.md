# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
# Start all applications
bun dev

# Start specific applications
bun dev:web      # Frontend only (port 3001)
bun dev:server   # Backend API only (port 3000)

# Build and type checking
bun build        # Build all applications
bun check-types  # TypeScript type checking across all apps

# Database operations
bun db:push      # Push schema changes to database
bun db:studio    # Open Drizzle Studio (database GUI)
bun db:generate  # Generate migrations
bun db:migrate   # Run migrations
```

## 🏗️ Architecture Overview

This is a full-stack TypeScript monorepo using **Turborepo** with two main applications:

### Apps Structure
- **`apps/web/`** - Next.js frontend (port 3001)
  - Uses App Router with TailwindCSS
  - shadcn/ui components with dark/light theme support
  - React Query + tRPC for type-safe API calls
  - Error handling with toast notifications via Sonner

- **`apps/server/`** - Next.js API backend (port 3000)
  - tRPC router with type-safe procedures
  - PostgreSQL database with Drizzle ORM
  - API routes exposed at `/trpc` endpoint

### Key Technologies
- **Database**: PostgreSQL + Drizzle ORM (schema at `apps/server/src/db/`)
- **API Layer**: tRPC for end-to-end type safety
- **Frontend**: Next.js 15 with React 19, TailwindCSS v4
- **Package Manager**: Bun
- **Monorepo**: Turborepo with workspace dependencies

### Data Flow
1. Frontend makes tRPC calls via `apps/web/src/utils/trpc.ts`
2. Requests hit tRPC router at `apps/server/src/routers/index.ts`
3. Server procedures interact with database via Drizzle at `apps/server/src/db/index.ts`
4. Type safety maintained across entire stack via shared `AppRouter` type

## 🔧 Environment Setup

Required environment files:
- `apps/server/.env`: `DATABASE_URL`, `DIRECT_URL`, `CORS_ORIGIN`
- `apps/web/.env`: `NEXT_PUBLIC_SERVER_URL`

## 🎨 UI Development

- Use shadcn/ui components from `apps/web/src/components/ui/`
- Theme provider supports dark/light mode switching
- Global styles in `apps/web/src/index.css`
- React Query DevTools available in development

## 📁 Key File Locations

- Database schema: `apps/server/src/db/schema/` (referenced in drizzle.config.ts)
- tRPC procedures: `apps/server/src/routers/index.ts`
- Frontend tRPC client: `apps/web/src/utils/trpc.ts`
- UI components: `apps/web/src/components/`
- Database migrations: `apps/server/src/db/migrations/`